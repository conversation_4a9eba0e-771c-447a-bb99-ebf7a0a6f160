{"name": "react-feather", "version": "2.0.10", "description": "React component for Feather icons", "main": "build/index.js", "module": "dist/index.js", "sideEffects": false, "typings": "dist/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "compile": "rm -rf src/icons && node bin/build.js", "build:bundle": "rm -rf build && rollup --config rollup.config.js", "build:es": "rm -rf dist && babel src --out-dir dist --copy-files", "build": "concurrently \"npm:build:*\""}, "files": ["dist", "es"], "repository": {"type": "git", "url": "git+https://github.com/feathericons/react-feather.git"}, "keywords": ["react", "icons", "svg", "inline", "feather", "design"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/feathericons/react-feather/issues"}, "homepage": "https://github.com/feathericons/react-feather#readme", "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.5", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/preset-env": "^7.4.5", "@babel/preset-react": "^7.0.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.7.0", "concurrently": "^5.1.0", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.1.0", "eslint-plugin-import": "^2.18.0", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.2", "feather-icons": "^4.29.0", "prettier-eslint": "^9.0.0", "rollup": "^2.3.3", "rollup-plugin-babel": "^4.3.3", "uppercamelcase": "^3.0.0"}, "peerDependencies": {"react": ">=16.8.6"}, "dependencies": {"prop-types": "^15.7.2"}}