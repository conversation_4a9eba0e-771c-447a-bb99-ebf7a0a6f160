{"name": "react-use-measure", "version": "2.1.7", "description": "Utility to measure view bounds", "keywords": ["react", "use", "measure", "bounds", "hooks"], "author": "<PERSON>", "homepage": "https://github.com/pmndrs/react-use-measure", "repository": "https://github.com/pmndrs/react-use-measure", "license": "MIT", "files": ["dist/*", "src/*"], "type": "module", "types": "./dist/index.d.ts", "main": "./dist/index.cjs", "module": "./dist/index.js", "exports": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "sideEffects": false, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/node": "^22.12.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitest/browser": "^3.0.4", "playwright": "^1.50.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resize-observer-polyfill": "^1.5.1", "rimraf": "^6.0.1", "typescript": "^5.7.3", "vite": "^6.0.11", "vitest": "^3.0.4"}, "peerDependencies": {"react": ">=16.13", "react-dom": ">=16.13"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}, "scripts": {"dev": "vite", "build": "rimraf dist && vite build && tsc", "test": "npx playwright install && vitest run"}}